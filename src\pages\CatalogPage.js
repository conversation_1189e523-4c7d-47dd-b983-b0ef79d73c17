import React from 'react';

const CatalogPage = () => {
  const containerStyle = {
    padding: '80px 60px',
    minHeight: '80vh',
    backgroundColor: '#f8f9fa'
  };

  const titleStyle = {
    fontSize: '48px',
    fontWeight: 'bold',
    color: '#1a3d2e',
    marginBottom: '30px',
    textAlign: 'center'
  };

  const contentStyle = {
    fontSize: '18px',
    color: '#666',
    textAlign: 'center',
    maxWidth: '600px',
    margin: '0 auto'
  };

  return (
    <div style={containerStyle}>
      <h1 style={titleStyle}>CATALOG</h1>
      <p style={contentStyle}>
        Explore our comprehensive catalog of premium sports equipment. 
        From skiing gear to outdoor adventure equipment, we have everything you need to push your limits.
      </p>
    </div>
  );
};

export default CatalogPage;
