import React from "react";

const HomePage = () => {
  const containerStyle = {
    display: "flex",
    minHeight: "100vh",
    backgroundColor: "#1a3d2e",
    position: "relative",
    overflow: "hidden",
  };

  const leftSectionStyle = {
    flex: "1",
    padding: "80px 60px",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    position: "relative",
    zIndex: 2,
  };

  const sideTextStyle = {
    position: "absolute",
    left: "20px",
    top: "50%",
    transform: "translateY(-50%) rotate(-90deg)",
    transformOrigin: "center",
    color: "#7cb342",
    fontSize: "12px",
    fontWeight: "500",
    letterSpacing: "2px",
    textTransform: "uppercase",
  };

  const numberStyle = {
    fontSize: "120px",
    fontWeight: "bold",
    color: "rgba(124, 179, 66, 0.3)",
    lineHeight: "1",
    marginBottom: "20px",
  };

  const taglineStyle = {
    color: "#7cb342",
    fontSize: "12px",
    fontWeight: "500",
    letterSpacing: "2px",
    textTransform: "uppercase",
    marginBottom: "20px",
  };

  const headingStyle = {
    color: "#ffffff",
    fontSize: "48px",
    fontWeight: "bold",
    lineHeight: "1.2",
    marginBottom: "30px",
    maxWidth: "400px",
  };

  const buttonStyle = {
    backgroundColor: "#7cb342",
    color: "#ffffff",
    border: "none",
    padding: "12px 30px",
    fontSize: "12px",
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: "1px",
    cursor: "pointer",
    transition: "all 0.3s ease",
    alignSelf: "flex-start",
    marginTop: "20px",
  };

  const rightSectionStyle = {
    flex: "1",
    position: "relative",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  };

  const imageStyle = {
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: "0",
  };

  const socialDotsStyle = {
    position: "absolute",
    right: "30px",
    top: "50%",
    transform: "translateY(-50%)",
    display: "flex",
    flexDirection: "column",
    gap: "15px",
  };

  const dotStyle = {
    width: "8px",
    height: "8px",
    borderRadius: "50%",
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    cursor: "pointer",
    transition: "all 0.3s ease",
  };

  const activeDotStyle = {
    ...dotStyle,
    backgroundColor: "#7cb342",
    transform: "scale(1.2)",
  };

  const paginationStyle = {
    position: "absolute",
    bottom: "30px",
    right: "30px",
    display: "flex",
    gap: "10px",
  };

  const paginationDotStyle = {
    width: "30px",
    height: "3px",
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    cursor: "pointer",
    transition: "all 0.3s ease",
  };

  const activePaginationDotStyle = {
    ...paginationDotStyle,
    backgroundColor: "#ffffff",
  };

  return (
    <div style={containerStyle}>
      <div style={sideTextStyle}>SPORT EQUIPMENT STORE</div>

      {/* Left Section */}
      <div style={leftSectionStyle}>
        <div style={numberStyle}>01</div>
        <div style={taglineStyle}>BEYOND BREAKING YOUR LIMIT</div>
        <h1 style={headingStyle}>KNOW YOUR LIMITS. SKI BEYOND THEM.</h1>
        <button
          style={buttonStyle}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#6ba639";
            e.target.style.transform = "translateY(-2px)";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#7cb342";
            e.target.style.transform = "translateY(0)";
          }}
        >
          VIEW MORE
        </button>
      </div>

      <div style={rightSectionStyle}>
        <img
          src="https://images.unsplash.com/photo-1551524164-6cf2ac531400?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
          alt="Skier in action"
          style={imageStyle}
        />
      </div>

      <div style={socialDotsStyle}>
        <div style={activeDotStyle}></div>
        <div style={dotStyle}></div>
        <div style={dotStyle}></div>
        <div style={dotStyle}></div>
      </div>

      {/* Pagination */}
      <div style={paginationStyle}>
        <div style={activePaginationDotStyle}></div>
        <div style={paginationDotStyle}></div>
      </div>
    </div>
  );
};

export default HomePage;
